﻿using GMCadiomChat.Desktop.WinForms.Controls;

namespace GMCadiomChat.Desktop.WinForms;

partial class ChatForm
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.headerPanel = new System.Windows.Forms.Panel();
        this.titleLabel = new System.Windows.Forms.Label();
        this.statusLabel = new System.Windows.Forms.Label();
        this.usernameTextBox = new System.Windows.Forms.TextBox();
        this.connectButton = new System.Windows.Forms.Button();
        this.mainPanel = new System.Windows.Forms.Panel();
        this.userListPanel = new System.Windows.Forms.Panel();
        this.userListHeaderLabel = new System.Windows.Forms.Label();
        this.userListContainer = new System.Windows.Forms.Panel();
        this.splitter = new System.Windows.Forms.Splitter();
        this.chatPanel = new System.Windows.Forms.Panel();
        this.chatHeaderPanel = new System.Windows.Forms.Panel();
        this.selectedUserLabel = new System.Windows.Forms.Label();
        this.selectedUserStatusLabel = new System.Windows.Forms.Label();
        this.buzzButton = new System.Windows.Forms.Button();
        this.messagesPanel = new System.Windows.Forms.Panel();
        this.typingLabel = new System.Windows.Forms.Label();
        this.inputPanel = new System.Windows.Forms.Panel();
        this.messageTextBox = new System.Windows.Forms.TextBox();
        this.sendButton = new System.Windows.Forms.Button();
        this.headerPanel.SuspendLayout();
        this.mainPanel.SuspendLayout();
        this.userListPanel.SuspendLayout();
        this.chatPanel.SuspendLayout();
        this.chatHeaderPanel.SuspendLayout();
        this.inputPanel.SuspendLayout();
        this.SuspendLayout();
        //
        // headerPanel
        //
        this.headerPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(150)))), ((int)(((byte)(243)))));
        this.headerPanel.Controls.Add(this.titleLabel);
        this.headerPanel.Controls.Add(this.statusLabel);
        this.headerPanel.Controls.Add(this.usernameTextBox);
        this.headerPanel.Controls.Add(this.connectButton);
        this.headerPanel.Dock = System.Windows.Forms.DockStyle.Top;
        this.headerPanel.Location = new System.Drawing.Point(0, 0);
        this.headerPanel.Name = "headerPanel";
        this.headerPanel.Padding = new System.Windows.Forms.Padding(16, 12, 16, 12);
        this.headerPanel.Size = new System.Drawing.Size(1200, 60);
        this.headerPanel.TabIndex = 0;
        //
        // titleLabel
        //
        this.titleLabel.AutoSize = true;
        this.titleLabel.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold);
        this.titleLabel.ForeColor = System.Drawing.Color.White;
        this.titleLabel.Location = new System.Drawing.Point(16, 18);
        this.titleLabel.Name = "titleLabel";
        this.titleLabel.Size = new System.Drawing.Size(142, 25);
        this.titleLabel.TabIndex = 0;
        this.titleLabel.Text = "GMCadiomChat";
        //
        // statusLabel
        //
        this.statusLabel.AutoSize = true;
        this.statusLabel.Font = new System.Drawing.Font("Segoe UI", 9F);
        this.statusLabel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(200)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
        this.statusLabel.Location = new System.Drawing.Point(180, 22);
        this.statusLabel.Name = "statusLabel";
        this.statusLabel.Size = new System.Drawing.Size(86, 15);
        this.statusLabel.TabIndex = 1;
        this.statusLabel.Text = "Disconnected";
        //
        // usernameTextBox
        //
        this.usernameTextBox.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
        this.usernameTextBox.Font = new System.Drawing.Font("Segoe UI", 9F);
        this.usernameTextBox.Location = new System.Drawing.Point(950, 18);
        this.usernameTextBox.Name = "usernameTextBox";
        this.usernameTextBox.PlaceholderText = "Enter username";
        this.usernameTextBox.Size = new System.Drawing.Size(150, 23);
        this.usernameTextBox.TabIndex = 2;
        //
        // connectButton
        //
        this.connectButton.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
        this.connectButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(25)))), ((int)(((byte)(118)))), ((int)(((byte)(210)))));
        this.connectButton.FlatAppearance.BorderSize = 0;
        this.connectButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
        this.connectButton.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
        this.connectButton.ForeColor = System.Drawing.Color.White;
        this.connectButton.Location = new System.Drawing.Point(1110, 18);
        this.connectButton.Name = "connectButton";
        this.connectButton.Size = new System.Drawing.Size(75, 23);
        this.connectButton.TabIndex = 3;
        this.connectButton.Text = "Connect";
        this.connectButton.UseVisualStyleBackColor = false;
        //
        // mainPanel
        //
        this.mainPanel.Controls.Add(this.userListPanel);
        this.mainPanel.Controls.Add(this.splitter);
        this.mainPanel.Controls.Add(this.chatPanel);
        this.mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
        this.mainPanel.Location = new System.Drawing.Point(0, 60);
        this.mainPanel.Name = "mainPanel";
        this.mainPanel.Size = new System.Drawing.Size(1200, 640);
        this.mainPanel.TabIndex = 1;
        //
        // userListPanel
        //
        this.userListPanel.BackColor = System.Drawing.Color.White;
        this.userListPanel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
        this.userListPanel.Controls.Add(this.userListHeaderLabel);
        this.userListPanel.Controls.Add(this.userListContainer);
        this.userListPanel.Dock = System.Windows.Forms.DockStyle.Left;
        this.userListPanel.Location = new System.Drawing.Point(0, 0);
        this.userListPanel.Name = "userListPanel";
        this.userListPanel.Size = new System.Drawing.Size(300, 640);
        this.userListPanel.TabIndex = 0;
        //
        // userListHeaderLabel
        //
        this.userListHeaderLabel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
        this.userListHeaderLabel.Dock = System.Windows.Forms.DockStyle.Top;
        this.userListHeaderLabel.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
        this.userListHeaderLabel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(33)))), ((int)(((byte)(33)))));
        this.userListHeaderLabel.Location = new System.Drawing.Point(0, 0);
        this.userListHeaderLabel.Name = "userListHeaderLabel";
        this.userListHeaderLabel.Padding = new System.Windows.Forms.Padding(16, 12, 16, 12);
        this.userListHeaderLabel.Size = new System.Drawing.Size(298, 40);
        this.userListHeaderLabel.TabIndex = 0;
        this.userListHeaderLabel.Text = "Online Users";
        //
        // userListContainer
        //
        this.userListContainer.AutoScroll = true;
        this.userListContainer.Dock = System.Windows.Forms.DockStyle.Fill;
        this.userListContainer.Location = new System.Drawing.Point(0, 40);
        this.userListContainer.Name = "userListContainer";
        this.userListContainer.Size = new System.Drawing.Size(298, 598);
        this.userListContainer.TabIndex = 1;
        //
        // splitter
        //
        this.splitter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
        this.splitter.Location = new System.Drawing.Point(300, 0);
        this.splitter.Name = "splitter";
        this.splitter.Size = new System.Drawing.Size(4, 640);
        this.splitter.TabIndex = 1;
        this.splitter.TabStop = false;
        //
        // chatPanel
        //
        this.chatPanel.BackColor = System.Drawing.Color.White;
        this.chatPanel.Controls.Add(this.chatHeaderPanel);
        this.chatPanel.Controls.Add(this.messagesPanel);
        this.chatPanel.Controls.Add(this.typingLabel);
        this.chatPanel.Controls.Add(this.inputPanel);
        this.chatPanel.Dock = System.Windows.Forms.DockStyle.Fill;
        this.chatPanel.Location = new System.Drawing.Point(304, 0);
        this.chatPanel.Name = "chatPanel";
        this.chatPanel.Size = new System.Drawing.Size(896, 640);
        this.chatPanel.TabIndex = 2;
        //
        // chatHeaderPanel
        //
        this.chatHeaderPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
        this.chatHeaderPanel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
        this.chatHeaderPanel.Controls.Add(this.selectedUserLabel);
        this.chatHeaderPanel.Controls.Add(this.selectedUserStatusLabel);
        this.chatHeaderPanel.Controls.Add(this.buzzButton);
        this.chatHeaderPanel.Dock = System.Windows.Forms.DockStyle.Top;
        this.chatHeaderPanel.Location = new System.Drawing.Point(0, 0);
        this.chatHeaderPanel.Name = "chatHeaderPanel";
        this.chatHeaderPanel.Padding = new System.Windows.Forms.Padding(16, 12, 16, 12);
        this.chatHeaderPanel.Size = new System.Drawing.Size(896, 60);
        this.chatHeaderPanel.TabIndex = 0;
        //
        // selectedUserLabel
        //
        this.selectedUserLabel.AutoSize = true;
        this.selectedUserLabel.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
        this.selectedUserLabel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(33)))), ((int)(((byte)(33)))));
        this.selectedUserLabel.Location = new System.Drawing.Point(16, 12);
        this.selectedUserLabel.Name = "selectedUserLabel";
        this.selectedUserLabel.Size = new System.Drawing.Size(234, 21);
        this.selectedUserLabel.TabIndex = 0;
        this.selectedUserLabel.Text = "Select a user to start chatting";
        //
        // selectedUserStatusLabel
        //
        this.selectedUserStatusLabel.AutoSize = true;
        this.selectedUserStatusLabel.Font = new System.Drawing.Font("Segoe UI", 9F);
        this.selectedUserStatusLabel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(117)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
        this.selectedUserStatusLabel.Location = new System.Drawing.Point(16, 33);
        this.selectedUserStatusLabel.Name = "selectedUserStatusLabel";
        this.selectedUserStatusLabel.Size = new System.Drawing.Size(0, 15);
        this.selectedUserStatusLabel.TabIndex = 1;
        //
        // buzzButton
        //
        this.buzzButton.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
        this.buzzButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(64)))), ((int)(((byte)(129)))));
        this.buzzButton.FlatAppearance.BorderSize = 0;
        this.buzzButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
        this.buzzButton.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
        this.buzzButton.ForeColor = System.Drawing.Color.White;
        this.buzzButton.Location = new System.Drawing.Point(800, 18);
        this.buzzButton.Name = "buzzButton";
        this.buzzButton.Size = new System.Drawing.Size(80, 25);
        this.buzzButton.TabIndex = 2;
        this.buzzButton.Text = "📳 Buzz";
        this.buzzButton.UseVisualStyleBackColor = false;
        //
        // messagesPanel
        //
        this.messagesPanel.AutoScroll = true;
        this.messagesPanel.Dock = System.Windows.Forms.DockStyle.Fill;
        this.messagesPanel.Location = new System.Drawing.Point(0, 60);
        this.messagesPanel.Name = "messagesPanel";
        this.messagesPanel.Padding = new System.Windows.Forms.Padding(16);
        this.messagesPanel.Size = new System.Drawing.Size(896, 520);
        this.messagesPanel.TabIndex = 1;
        //
        // typingLabel
        //
        this.typingLabel.Dock = System.Windows.Forms.DockStyle.Bottom;
        this.typingLabel.Font = new System.Drawing.Font("Segoe UI", 8F, System.Drawing.FontStyle.Italic);
        this.typingLabel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(117)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
        this.typingLabel.Location = new System.Drawing.Point(0, 580);
        this.typingLabel.Name = "typingLabel";
        this.typingLabel.Padding = new System.Windows.Forms.Padding(16, 4, 16, 4);
        this.typingLabel.Size = new System.Drawing.Size(896, 20);
        this.typingLabel.TabIndex = 2;
        //
        // inputPanel
        //
        this.inputPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
        this.inputPanel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
        this.inputPanel.Controls.Add(this.messageTextBox);
        this.inputPanel.Controls.Add(this.sendButton);
        this.inputPanel.Dock = System.Windows.Forms.DockStyle.Bottom;
        this.inputPanel.Location = new System.Drawing.Point(0, 600);
        this.inputPanel.Name = "inputPanel";
        this.inputPanel.Padding = new System.Windows.Forms.Padding(16);
        this.inputPanel.Size = new System.Drawing.Size(896, 40);
        this.inputPanel.TabIndex = 3;
        //
        // messageTextBox
        //
        this.messageTextBox.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
        | System.Windows.Forms.AnchorStyles.Right)));
        this.messageTextBox.Font = new System.Drawing.Font("Segoe UI", 9F);
        this.messageTextBox.Location = new System.Drawing.Point(16, 8);
        this.messageTextBox.Name = "messageTextBox";
        this.messageTextBox.PlaceholderText = "Type a message...";
        this.messageTextBox.Size = new System.Drawing.Size(780, 23);
        this.messageTextBox.TabIndex = 0;
        //
        // sendButton
        //
        this.sendButton.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
        this.sendButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(150)))), ((int)(((byte)(243)))));
        this.sendButton.FlatAppearance.BorderSize = 0;
        this.sendButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
        this.sendButton.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
        this.sendButton.ForeColor = System.Drawing.Color.White;
        this.sendButton.Location = new System.Drawing.Point(805, 8);
        this.sendButton.Name = "sendButton";
        this.sendButton.Size = new System.Drawing.Size(75, 23);
        this.sendButton.TabIndex = 1;
        this.sendButton.Text = "Send";
        this.sendButton.UseVisualStyleBackColor = false;
        //
        // ChatForm
        //
        this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
        this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        this.ClientSize = new System.Drawing.Size(1200, 700);
        this.Controls.Add(this.mainPanel);
        this.Controls.Add(this.headerPanel);
        this.MinimumSize = new System.Drawing.Size(800, 500);
        this.Name = "ChatForm";
        this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
        this.Text = "GMCadiomChat";
        this.headerPanel.ResumeLayout(false);
        this.headerPanel.PerformLayout();
        this.mainPanel.ResumeLayout(false);
        this.userListPanel.ResumeLayout(false);
        this.chatPanel.ResumeLayout(false);
        this.chatHeaderPanel.ResumeLayout(false);
        this.chatHeaderPanel.PerformLayout();
        this.inputPanel.ResumeLayout(false);
        this.inputPanel.PerformLayout();
        this.ResumeLayout(false);

    }

    #endregion

    private System.Windows.Forms.Panel headerPanel;
    private System.Windows.Forms.Label titleLabel;
    private System.Windows.Forms.Label statusLabel;
    private System.Windows.Forms.TextBox usernameTextBox;
    private System.Windows.Forms.Button connectButton;
    private System.Windows.Forms.Panel mainPanel;
    private System.Windows.Forms.Panel userListPanel;
    private System.Windows.Forms.Label userListHeaderLabel;
    private System.Windows.Forms.Panel userListContainer;
    private System.Windows.Forms.Splitter splitter;
    private System.Windows.Forms.Panel chatPanel;
    private System.Windows.Forms.Panel chatHeaderPanel;
    private System.Windows.Forms.Label selectedUserLabel;
    private System.Windows.Forms.Label selectedUserStatusLabel;
    private System.Windows.Forms.Button buzzButton;
    private System.Windows.Forms.Panel messagesPanel;
    private System.Windows.Forms.Label typingLabel;
    private System.Windows.Forms.Panel inputPanel;
    private System.Windows.Forms.TextBox messageTextBox;
    private System.Windows.Forms.Button sendButton;
}
