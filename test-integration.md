# GMCadiomChat Integration Test Guide

## Quick Start Test

### 1. Start the Server
```bash
cd GMCadiomChat.Server
dotnet run
```
Server should start on: `http://localhost:5263`

### 2. Verify Server Health
```bash
curl http://localhost:5263/health
```
Expected response: `{"status":"Healthy","timestamp":"..."}`

### 3. Test SignalR Hub
The SignalR hub is available at: `http://localhost:5263/chathub`

### 4. Start Desktop Client
```bash
cd GMCadiomChat.Desktop.WinForms
dotnet run
```

### 5. Test Basic Functionality

#### Login Test:
1. Enter username (e.g., "TestUser1")
2. Click "Connect"
3. Verify connection status shows "Connected"
4. Check that user appears in online users list

#### Multi-User Test:
1. Start second instance of desktop client
2. Enter different username (e.g., "TestUser2")
3. Connect second user
4. Verify both users see each other in online users list

#### Messaging Test:
1. Select a user from the online users list
2. Type a message in the message input
3. Press Enter or click Send
4. Verify message appears in chat area
5. Verify message appears on recipient's screen

#### Buzz Test:
1. Select a user from the online users list
2. Click the "📳 Buzz" button
3. Verify recipient receives buzz notification
4. Verify recipient's window shakes (if supported)

## API Endpoints

### Health Check
- **GET** `/health`
- Returns server health status

### Chat API
- **GET** `/api/chat/online-users`
- **GET** `/api/chat/user/{userId}/messages`
- **GET** `/api/chat/conversation/{user1Id}/{user2Id}`
- **POST** `/api/chat/message/{messageId}/mark-read`
- **GET** `/api/chat/user/{userId}/unread-count/{fromUserId}`

### SignalR Hub
- **Hub URL**: `/chathub`
- **Methods**: JoinChat, LeaveChat, SendMessage, SendBuzz, SetTyping, etc.

## Expected Behavior

### Connection Flow:
1. Desktop client connects to SignalR hub
2. Client sends JoinChat with username/email
3. Server creates/updates user record
4. Server broadcasts UserConnected to all clients
5. Server sends undelivered messages to newly connected user
6. Server sends list of online users to new client

### Messaging Flow:
1. User types message and sends
2. Client sends SendMessage to server
3. Server creates message record in database
4. Server broadcasts message to recipient (if online)
5. Server sends delivery confirmation
6. Recipient can mark message as read

### Presence Flow:
1. User status changes (online/away/busy/offline)
2. Server updates user status in database
3. Server broadcasts UserStatusChanged to all clients
4. All clients update their user lists

## Troubleshooting

### Common Issues:

1. **Server won't start**
   - Check if port 5263 is available
   - Verify .NET 8 SDK is installed
   - Check for build errors

2. **Desktop client won't connect**
   - Verify server is running
   - Check SignalR hub URL in client configuration
   - Check for firewall blocking connection

3. **Messages not delivering**
   - Verify both users are connected
   - Check server logs for errors
   - Verify database is accessible

4. **UI not responding**
   - Check for exceptions in desktop client
   - Verify MVVM bindings are correct
   - Check for threading issues

### Logs to Check:
- Server console output
- Desktop client debug output
- Entity Framework logs (if enabled)
- SignalR connection logs

## Performance Testing

### Load Testing:
1. Start server
2. Connect multiple desktop clients (10-50)
3. Send messages simultaneously
4. Monitor server performance and memory usage

### Stress Testing:
1. Send rapid messages between clients
2. Connect/disconnect users frequently
3. Send large messages or files
4. Monitor for memory leaks or performance degradation

## Configuration Testing

### Database Providers:
Test with different database configurations:

1. **In-Memory** (default)
2. **SQL Server**
3. **SQLite**

Update `Program.cs` in server project to test different providers.

### SignalR Configuration:
Test different SignalR settings:
- Connection timeouts
- Reconnection attempts
- Message size limits
- Concurrent connections

## Security Testing

### Input Validation:
1. Test with empty usernames
2. Test with very long messages
3. Test with special characters
4. Test with HTML/script injection attempts

### Connection Security:
1. Test unauthorized access attempts
2. Test connection flooding
3. Test message spam prevention

## Success Criteria

✅ Server starts without errors
✅ Health endpoint responds correctly
✅ Desktop client connects successfully
✅ Users can see each other online
✅ Messages are sent and received in real-time
✅ Message history is persisted
✅ Buzz notifications work
✅ Typing indicators function
✅ Connection recovery works after network issues
✅ Multiple clients can connect simultaneously
✅ UI is responsive and user-friendly
