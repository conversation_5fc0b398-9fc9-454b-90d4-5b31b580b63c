# GMCadiomChat

A comprehensive real-time chat application built with .NET 8, featuring a modern WPF desktop client and ASP.NET Core SignalR server.

## 🏗️ Architecture

The application follows a clean 3-layer architecture:

### 1. **GMCadiomChat.Shared** - Shared Models & Contracts
- DTOs for data transfer between client and server
- Enums for message types and user status
- SignalR hub interfaces

### 2. **GMCadiomChat.Core** - Business Logic Layer
- Entity models with inheritance hierarchy
- Repository pattern implementation
- Business services and interfaces
- Entity Framework Core data layer
- Configurable storage options (In-Memory, SQL Server, SQLite)

### 3. **GMCadiomChat.Server** - ASP.NET Core Server
- SignalR hubs for real-time communication
- RESTful API controllers
- Dependency injection configuration
- CORS support for cross-origin requests

### 4. **GMCadiomChat.Desktop.WinForms** - Windows Forms Desktop Client
- Modern MVVM architecture using CommunityToolkit.Mvvm
- Real-time SignalR client
- Desktop notifications and window animations
- Material Design-inspired UI with custom controls

## 🚀 Features

### Core Messaging
- **Real-time messaging** between online users
- **Offline message delivery** when users come online
- **Message history** persistence and retrieval
- **Message status indicators** (sent, delivered, read)
- **Typing indicators** to show when users are typing

### Message Types (Polymorphic Design)
1. **Text Messages** - Plain text communication
2. **Image Messages** - Image sharing with metadata
3. **Audio Messages** - Voice recordings with duration
4. **Screen Share Messages** - Live screen sharing sessions
5. **Buzz Messages** - Attention-grabbing notifications with window shaking

### User Management
- **User presence tracking** (Online, Away, Busy, Offline)
- **Real-time user list** with status indicators
- **Connection management** with automatic reconnection
- **User authentication** via username/email

### Desktop Features
- **Modern UI** with Material Design principles
- **Desktop notifications** for new messages and buzzes
- **Window shaking animation** for buzz messages
- **Sound notifications** for different message types
- **Responsive design** with resizable panels
- **Dark/Light theme support** (configurable)

## 🛠️ Technology Stack

- **.NET 8** - Target framework
- **ASP.NET Core** - Server framework
- **SignalR** - Real-time communication
- **Entity Framework Core** - Data access layer
- **Windows Forms** - Desktop UI framework
- **CommunityToolkit.Mvvm** - MVVM framework
- **SQL Server / SQLite / In-Memory** - Database options

## 📦 Project Structure

```
GMCadiomChat/
├── GMCadiomChat.Shared/          # Shared models and contracts
│   ├── DTOs/                     # Data transfer objects
│   ├── Enums/                    # Enumerations
│   └── Interfaces/               # SignalR hub interfaces
├── GMCadiomChat.Core/            # Business logic and data layer
│   ├── Entities/                 # Domain entities
│   ├── Data/                     # DbContext and configurations
│   ├── Repositories/             # Repository implementations
│   ├── Services/                 # Business services
│   ├── Interfaces/               # Service interfaces
│   ├── Configuration/            # Configuration models
│   └── Extensions/               # Dependency injection extensions
├── GMCadiomChat.Server/          # ASP.NET Core server
│   ├── Hubs/                     # SignalR hubs
│   ├── Controllers/              # API controllers
│   └── Program.cs                # Server configuration
└── GMCadiomChat.Desktop.WinForms/ # Windows Forms desktop client
    ├── ViewModels/               # MVVM view models
    ├── Services/                 # Client services
    ├── Controls/                 # Custom UI controls
    ├── Form1.cs                  # Main form
    └── Program.cs                # Application entry point
```

## 🚀 Getting Started

### Prerequisites
- .NET 8 SDK
- Visual Studio 2022 or VS Code
- SQL Server (optional, can use in-memory database)

### Running the Application

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd GMCadiomChat
   ```

2. **Build the solution**
   ```bash
   dotnet build
   ```

3. **Start the server**
   ```bash
   cd GMCadiomChat.Server
   dotnet run
   ```
   The server will start on `http://localhost:5000`

4. **Start the desktop client**
   ```bash
   cd GMCadiomChat.Desktop.WinForms
   dotnet run
   ```

5. **Connect to chat**
   - Enter a username in the desktop application
   - Click "Connect" to join the chat
   - Start multiple instances to test real-time communication

## ⚙️ Configuration

### Database Configuration
The application supports multiple database providers. Configure in `Program.cs`:

```csharp
builder.Services.AddGMCadiomChatCore(config =>
{
    // Use In-Memory database (default)
    config.Database.Provider = "InMemory";

    // Or use SQL Server
    config.Database.Provider = "SqlServer";
    config.Database.ConnectionString = "Server=.;Database=GMCadiomChat;Trusted_Connection=true;";

    // Or use SQLite
    config.Database.Provider = "SQLite";
    config.Database.ConnectionString = "Data Source=gmcadiomchat.db";
});
```

### SignalR Configuration
Configure SignalR settings:

```csharp
config.SignalR.HubUrl = "http://localhost:5000/chathub";
config.SignalR.ReconnectDelaySeconds = 5;
config.SignalR.MaxReconnectAttempts = 10;
```

## 🔧 Integration Guide

### Using GMCadiomChat.Core in Your Application

1. **Add package reference**
   ```xml
   <PackageReference Include="GMCadiomChat.Core" Version="1.0.0" />
   ```

2. **Configure services**
   ```csharp
   services.AddGMCadiomChatCore(config =>
   {
       config.Database.Provider = "SqlServer";
       config.Database.ConnectionString = "your-connection-string";
   });
   ```

3. **Use chat service**
   ```csharp
   public class YourController : ControllerBase
   {
       private readonly IChatService _chatService;

       public YourController(IChatService chatService)
       {
           _chatService = chatService;
       }

       public async Task<IActionResult> SendMessage(SendMessageRequest request)
       {
           var message = await _chatService.SendMessageAsync(userId, request);
           return Ok(message);
       }
   }
   ```

## 🎨 UI Features

### Modern Design Elements
- **Material Design** color scheme and components
- **Custom Windows Forms controls** for modern appearance
- **Responsive layout** with resizable panels
- **Status indicators** for users and messages
- **Typing indicators** with real-time updates
- **Message bubbles** with sender alignment and rounded corners
- **Unread message counters** with badge notifications

### Keyboard Shortcuts
- **Enter** - Send message
- **Shift + Enter** - New line in message
- **Escape** - Clear message input

## 🔒 Security Considerations

- Input validation on all user inputs
- SQL injection protection through EF Core
- XSS protection in message content
- Connection authentication and authorization
- Rate limiting for message sending (configurable)

## 🧪 Testing

### Running Tests
```bash
dotnet test
```

### Test Multiple Clients
1. Start the server
2. Run multiple instances of the desktop client
3. Connect with different usernames
4. Test real-time messaging, buzzes, and presence updates

## 📈 Performance Features

- **Connection pooling** for database operations
- **Efficient SignalR** connection management
- **Message pagination** for large conversation histories
- **Lazy loading** of user data and messages
- **Optimized UI virtualization** for large user lists

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🎯 Implementation Status

### ✅ Completed Features
- **3-Layer Architecture**: Shared, Core, Server, and Desktop projects
- **Entity Framework Core**: With configurable storage (In-Memory, SQL Server, SQLite)
- **SignalR Real-time Communication**: Full hub implementation with client/server
- **Message Type Inheritance**: Text, Image, Audio, ScreenShare, and Buzz messages
- **Modern Windows Forms Desktop UI**: MVVM pattern with custom controls and Material Design styling
- **User Presence Management**: Online/offline status tracking
- **Message History & Persistence**: Database storage with retrieval
- **Desktop Notifications**: Sound alerts and window shaking for buzz
- **Typing Indicators**: Real-time typing status
- **Message Status**: Sent, delivered, and read indicators
- **Connection Management**: Auto-reconnection and error handling
- **RESTful API**: Additional HTTP endpoints for chat operations
- **Comprehensive Documentation**: README, integration tests, and code comments

### 🚀 Ready to Run
The application is fully functional and ready for testing:

1. **Server**: Runs on `http://localhost:5263` with SignalR hub at `/chathub`
2. **Desktop Client**: Modern Windows Forms application with real-time chat capabilities
3. **Database**: Configured for in-memory storage (easily switchable to SQL Server/SQLite)
4. **Integration**: All components work together seamlessly

### 🧪 Testing
- All projects build successfully
- Server starts and responds to health checks
- SignalR hub is properly configured
- Desktop UI is complete with modern styling
- Integration test guide provided

## 🙏 Acknowledgments

- Built with .NET 8 and modern development practices
- Uses SignalR for real-time communication
- Inspired by modern chat applications like Discord and Slack
- UI design influenced by Material Design principles
- Implements clean architecture and SOLID principles
